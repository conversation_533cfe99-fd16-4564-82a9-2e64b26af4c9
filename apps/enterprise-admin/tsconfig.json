{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "resolveJsonModule": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node"], "outDir": "dist", "rootDir": "src", "tsBuildInfoFile": "dist/tsconfig.tsbuildinfo"}, "include": ["../../apps/enterprise-admin/.next/types/**/*.ts", "../../dist/apps/enterprise-admin/.next/types/**/*.ts", "next-env.d.ts", "src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", ".next/types/**/*.ts", "src/**/*.json"], "exclude": ["out-tsc", "dist", "node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", ".next", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "references": [{"path": "../../libs/ui"}, {"path": "../../libs/shared"}]}