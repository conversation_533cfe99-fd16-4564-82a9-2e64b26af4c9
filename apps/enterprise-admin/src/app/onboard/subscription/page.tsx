'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  CreditCard,
  Check,
  Star,
  Building,
  Users,
  Shield,
  Zap,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface PlanFeature {
  text: string;
  included: boolean;
}

interface Plan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: PlanFeature[];
  popular?: boolean;
  buttonText: string;
}

export default function SubscriptionPage() {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<string>('pro');
  const [isLoading, setIsLoading] = useState(false);

  const plans: Plan[] = [
    {
      name: 'Starter',
      price: '$29',
      period: '/month',
      description: 'Perfect for small teams getting started',
      features: [
        { text: 'Up to 5 team members', included: true },
        { text: 'Basic AI features', included: true },
        { text: 'Email support', included: true },
        { text: 'Basic analytics', included: true },
        { text: 'Advanced AI features', included: false },
        { text: 'Priority support', included: false },
      ],
      buttonText: 'Start Free Trial',
    },
    {
      name: 'Professional',
      price: '$99',
      period: '/month',
      description: 'Best for growing businesses',
      features: [
        { text: 'Up to 25 team members', included: true },
        { text: 'Advanced AI features', included: true },
        { text: 'Priority support', included: true },
        { text: 'Advanced analytics', included: true },
        { text: 'Custom integrations', included: true },
        { text: 'White-label options', included: false },
      ],
      popular: true,
      buttonText: 'Start Free Trial',
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: '',
      description: 'For large organizations with custom needs',
      features: [
        { text: 'Unlimited team members', included: true },
        { text: 'All AI features', included: true },
        { text: '24/7 dedicated support', included: true },
        { text: 'Custom analytics', included: true },
        { text: 'Custom integrations', included: true },
        { text: 'White-label options', included: true },
      ],
      buttonText: 'Contact Sales',
    },
  ];

  const handlePlanSelect = async (planName: string) => {
    setSelectedPlan(planName);
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      if (planName === 'Enterprise') {
        // Redirect to contact sales
        window.location.href =
          'mailto:<EMAIL>?subject=Enterprise Plan Inquiry';
      } else {
        // Redirect to dashboard for other plans
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Subscription error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8">
      <div className="w-full max-w-6xl">
        <Card className="rounded-2xl shadow-lg relative">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8 relative">
              <Button
                variant="ghost"
                className="absolute top-0 left-0 p-2 text-[#041C91] hover:bg-white"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="w-16 h-16 bg-[#041C91] rounded-full flex items-center justify-center mx-auto mb-6">
                <CreditCard className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-[#041C91] mb-2">
                Choose Your Plan
              </h1>
              <p className="text-gray-500 text-lg">
                Select the perfect plan for your business needs
              </p>
            </div>

            {/* Plans Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {plans.map((plan) => (
                <div
                  key={plan.name}
                  className={`relative rounded-xl border-2 p-6 transition-all cursor-pointer ${
                    plan.popular
                      ? 'border-[#041C91] bg-[#041C91]/5'
                      : selectedPlan === plan.name.toLowerCase()
                      ? 'border-[#041C91]'
                      : 'border-gray-200 hover:border-[#041C91]/50'
                  }`}
                  onClick={() => setSelectedPlan(plan.name.toLowerCase())}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-[#041C91] text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                        <Star className="w-3 h-3" />
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-[#041C91] mb-2">
                      {plan.name}
                    </h3>
                    <div className="flex items-baseline justify-center gap-1">
                      <span className="text-3xl font-bold text-gray-900">
                        {plan.price}
                      </span>
                      <span className="text-gray-500">{plan.period}</span>
                    </div>
                    <p className="text-gray-500 text-sm mt-2">
                      {plan.description}
                    </p>
                  </div>

                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <Check
                          className={`w-4 h-4 ${
                            feature.included
                              ? 'text-green-500'
                              : 'text-gray-300'
                          }`}
                        />
                        <span
                          className={`text-sm ${
                            feature.included ? 'text-gray-700' : 'text-gray-400'
                          }`}
                        >
                          {feature.text}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handlePlanSelect(plan.name)}
                    disabled={isLoading}
                    className={`w-full h-12 rounded-lg font-medium transition-colors ${
                      plan.popular
                        ? 'bg-[#041C91] hover:bg-[#01033d] text-white'
                        : 'bg-white border-2 border-[#041C91] text-[#041C91] hover:bg-[#041C91] hover:text-white'
                    }`}
                  >
                    {isLoading && selectedPlan === plan.name.toLowerCase()
                      ? 'Processing...'
                      : plan.buttonText}
                  </Button>
                </div>
              ))}
            </div>

            {/* Features Section */}
            <div className="border-t pt-8">
              <h2 className="text-xl font-bold text-[#041C91] text-center mb-6">
                Why Choose B6AI?
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#041C91]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Zap className="w-6 h-6 text-[#041C91]" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Lightning Fast
                  </h3>
                  <p className="text-sm text-gray-500">
                    Get instant responses with our advanced AI technology
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#041C91]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Shield className="w-6 h-6 text-[#041C91]" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Secure & Private
                  </h3>
                  <p className="text-sm text-gray-500">
                    Enterprise-grade security for your data
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#041C91]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-[#041C91]" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Team Collaboration
                  </h3>
                  <p className="text-sm text-gray-500">
                    Work together seamlessly with your team
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-[#041C91]/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Building className="w-6 h-6 text-[#041C91]" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">
                    Enterprise Ready
                  </h3>
                  <p className="text-sm text-gray-500">
                    Scales with your business needs
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
