'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { ArrowLeft, Building, User, Globe, CheckCircle } from 'lucide-react';
import { useOnboardTenantMutation, OnboardTenantDto } from '@b6ai/shared';
import { Button } from 'libs/ui/src';

interface FormData {
  firstName: string;
  lastName: string;
  tenantName: string;
  contactEmail: string;
  domain: string;
}

export default function CompanyDetailsPage() {
  const router = useRouter();
  const [email, setEmail] = useState<string | null>(null);
  const [onboardTenant, { isLoading, isError, isSuccess }] =
    useOnboardTenantMutation();

  // load email from localStorage and handle reload
  useEffect(() => {
    const storedEmail = localStorage.getItem('verifiedEmail');
    if (!storedEmail) {
      router.push('/onboard');
      return;
    }
    setEmail(storedEmail);

    const isPageReload =
      performance.navigation.type === performance.navigation.TYPE_RELOAD;
    if (isPageReload) {
      localStorage.removeItem('verifiedEmail');
      router.push('/onboard');
    }
  }, [router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      tenantName: '',
      contactEmail: '',
      domain: '',
    },
  });

  // sync contact email into form
  useEffect(() => {
    if (email) {
      reset((prev) => ({ ...prev, contactEmail: email }));
    }
  }, [email, reset]);

  const onSubmit = async (data: FormData) => {
    const payload: OnboardTenantDto = {
      firstName: data.firstName,
      lastName: data.lastName,
      adminEmail: data.contactEmail,
      tenantName: data.tenantName,
      customDomain: data.domain,
      idempotencyKey: crypto.randomUUID(),
    };

    await onboardTenant(payload).unwrap();
  };

  if (!email)
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-500">Loading your email...</p>
      </div>
    );

  if (isSuccess)
    return (
      <div className="min-h-screen flex items-center justify-center p-8 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 text-center border-2 border-primary">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-primary mb-4">
              Company Setup Complete!
            </h1>
            <p className="text-muted-foreground mb-8">
              Your company details have been saved. Continue to subscription.
            </p>
            <button
              className="w-full h-12 bg-gradient-to-r from-b6ai-blue to-b6ai-cyan text-white rounded-lg hover:opacity-90 transition-opacity"
              onClick={() => router.push('/onboard/subscription')}
            >
              Continue to Subscription
            </button>
          </div>
        </div>
      </div>
    );

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="flex-1 flex items-center justify-center py-12">
        <div className="w-full max-w-2xl p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="text-center mb-8 relative">
            <button
              className="absolute top-0 left-0 p-2 text-primary hover:bg-background rounded"
              onClick={() => window.history.back()}
            >
              <ArrowLeft size={20} />
            </button>
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <Building
                className="text-b6ai-blue dark:text-b6ai-cyan"
                size={24}
              />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Company Details
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Provide your company info to complete setup
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Names */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {['firstName', 'lastName'].map((field) => (
                <div key={field}>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {field === 'firstName' ? 'First Name' : 'Last Name'}{' '}
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <User
                      className="absolute left-3 top-3 text-gray-500 dark:text-gray-400"
                      size={18}
                    />
                    <input
                      type="text"
                      {...register(field as keyof FormData, {
                        required: `${
                          field === 'firstName' ? 'First' : 'Last'
                        } name is required`,
                        minLength:
                          field === 'firstName'
                            ? { value: 2, message: 'At least 2 characters' }
                            : undefined,
                      })}
                      placeholder={field === 'firstName' ? 'John' : 'Doe'}
                      className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                        errors[field as keyof FormData]
                          ? 'border-red-500'
                          : 'border-gray-300 dark:border-gray-600'
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                    />
                  </div>
                  {errors[field as keyof FormData] && (
                    <p className="mt-2 text-sm text-red-500">
                      {errors[field as keyof FormData]?.message?.toString()}
                    </p>
                  )}
                </div>
              ))}
            </div>

            {/* Company Name */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Company Name <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Building
                  className="absolute left-3 top-3 text-gray-500 dark:text-gray-400"
                  size={18}
                />
                <input
                  type="text"
                  {...register('tenantName', {
                    required: 'Company name is required',
                    minLength: { value: 3, message: 'At least 3 characters' },
                  })}
                  placeholder="Acme Corporation"
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                    errors.tenantName
                      ? 'border-red-500'
                      : 'border-gray-300 dark:border-gray-600'
                  } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                />
              </div>
              {errors.tenantName && (
                <p className="mt-2 text-sm text-red-500">
                  {errors.tenantName.message}
                </p>
              )}
            </div>

            {/* Admin Email */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Admin Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                {...register('contactEmail')}
                readOnly
                className="w-full px-4 py-3 rounded-lg border bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
            </div>

            {/* Subdomain */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subdomain <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Globe
                  className="absolute left-3 top-3 text-gray-500 dark:text-gray-400"
                  size={18}
                />
                <input
                  type="text"
                  {...register('domain', {
                    required: 'Subdomain is required',
                    pattern: {
                      value: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]$/,
                      message: 'Invalid subdomain format',
                    },
                  })}
                  placeholder="your-company"
                  className={`w-full pl-10 pr-20 py-3 rounded-lg border ${
                    errors.domain
                      ? 'border-red-500'
                      : 'border-gray-300 dark:border-gray-600'
                  } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan`}
                />
                <span className="absolute right-3 top-3 text-gray-500 dark:text-gray-400">
                  .b6ai.app
                </span>
              </div>
              {errors.domain ? (
                <p className="mt-2 text-sm text-red-500">
                  {errors.domain.message}
                </p>
              ) : (
                <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  This will be your unique URL:{' '}
                  <span className="font-medium">[your-subdomain].b6ai.app</span>
                </p>
              )}
            </div>

            {/* Submit */}
            <div className="pt-6">
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full px-4 py-2 btn-b6ai rounded-md "
              >
                {isLoading ? 'Creating...' : 'Continue to Subscription'}
              </Button>
              {isError && (
                <p className="text-destructive text-sm mt-2">
                  Something went wrong while creating tenant.
                </p>
              )}
            </div>
          </form>
        </div>
      </div>

      <footer className="py-6">
        <div className="container mx-auto px-6">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            © 2023 B6AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
