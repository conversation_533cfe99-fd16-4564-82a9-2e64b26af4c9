'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Input, Button } from '@b6ai/ui';
import { CheckCircle, XCircle } from 'lucide-react';
import Image from 'next/image';

export default function OnboardPage() {
  const [email, setEmail] = useState('');
  const router = useRouter();

  // Simple email regex validation
  const isValidEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const isValid = email ? isValidEmail(email) : null;

  const handleSignUp = () => {
    if (isValid) {
      localStorage.setItem('signupEmail', email); // ✅ save email
      router.push('/onboard/email-verification'); // ✅ navigate
    } else {
      alert('Please enter a valid email address.');
    }
  };

  return (
    <div className="min-h-screen flex bg-[white]">
      {/* Left section */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-[#041C91] leading-tight">
              <span className="whitespace-nowrap">Connect With Us and</span>{' '}
              <br />
              <span className="whitespace-nowrap">
                Unlock Smarter Interactions
              </span>
            </h1>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email Address
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className={`w-full h-12 px-4 text-base rounded-lg
                                 ${
                                   isValid === null
                                     ? 'border-gray-300 focus:border-gray-300 focus:ring-gray-300'
                                     : ''
                                 } 
                  ${
                    isValid === false
                      ? 'border-red-500 focus:border-red-600 focus:ring-red-200'
                      : ''
                  }`}
            />
            {isValid === true && (
              <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 w-5 h-5" />
            )}
            {isValid === false && (
              <XCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500 w-5 h-5" />
            )}
          </div>

          <Button
            onClick={handleSignUp}
            className="w-full px-4 py-2 btn-b6ai rounded-md"
          >
            Sign up
          </Button>

          {/* Divider */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 h-px bg-b6ai-gradient-bg "></div>
            <span className="text-sm text-gray-500 font-medium">
              Or continue with
            </span>
            <div className="flex-1 h-px bg-b6ai-gradient-bg "></div>
          </div>

          {/* Social buttons */}
          <div className="flex gap-4">
            <Button
              variant="outline"
              className="flex-1 h-12 border-gray-300 hover:border-[#041C91] text-[#01033D] rounded-lg font-medium transition-colors"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Google
            </Button>
            <Button
              variant="outline"
              className="flex-1 h-12 border-gray-300 hover:border-[#041C91] text-[#01033D] rounded-lg font-medium transition-colors"
            >
              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path fill="#00BCF2" d="M0 0h11.377v11.372H0z" />
                <path fill="#00BCF2" d="M12.623 0H24v11.372H12.623z" />
                <path fill="#00BCF2" d="M0 12.623h11.377V24H0z" />
                <path fill="#FFC107" d="M12.623 12.623H24V24H12.623z" />
              </svg>
              Microsoft
            </Button>
          </div>

          {/* Footer */}
          <div className="text-center pt-4">
            <p className="text-sm text-gray-600">
              Trying to access B6AI?{' '}
              <Button
                variant="link"
                className="text-primary p-0 h-auto font-medium hover:bg-transparent"
              >
                Log in
              </Button>
            </p>
          </div>
        </div>
      </div>

      {/* Right section */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center text-gray-500">
          <div className="mb-6 flex justify-center">
            <Image
              src="/images/bot.gif"
              alt="B6AI Logo"
              width={600}
              height={120}
              className="object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
