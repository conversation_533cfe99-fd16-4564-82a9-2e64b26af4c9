import './global.css';
import '@b6ai/ui/styles/globals.css';
import { LocalizationProvider } from './context/localization-context';
import { ToastProvider } from './context/toast-context';
import BrandProvider from '@b6ai/shared/providers/brand-provider';

export const metadata = {
  title: 'Welcome to enterprise-admin',
  description: 'Generated by create-nx-workspace',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <BrandProvider>
          <LocalizationProvider>
            <ToastProvider>{children}</ToastProvider>
          </LocalizationProvider>
        </BrandProvider>
      </body>
    </html>
  );
}
